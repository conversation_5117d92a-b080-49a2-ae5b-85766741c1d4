# Google Service Account Configuration
# Google credentials are now loaded from google-service-account.json file
# No need for environment variables for Google authentication

# Google Testing Configuration (optional - can be passed as command line arguments)
# GOOGLE_ADMIN_EMAIL=<EMAIL>
# GOOGLE_TEST_EMAIL=<EMAIL>
# GOOGLE_TEST_USERS=<EMAIL>,<EMAIL>

# Lark Configuration
LARK_APP_ID=your-lark-app-id
LARK_APP_SECRET=your-lark-app-secret
LARK_TENANT_ACCESS_TOKEN=your-tenant-access-token

# Supabase Configuration
SUPABASE_URL=your-supabase-url
SUPABASE_ANON_KEY=your-supabase-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-supabase-service-role-key

# Application Configuration
NODE_ENV=development
PORT=3000
LOG_LEVEL=info