import express from 'express';
import cors from 'cors';
import path from 'path';
import { fileURLToPath } from 'url';
import dotenv from 'dotenv';

// Load environment variables first
dotenv.config();

// Import configuration to validate on startup
import { googleConfig } from './config/google-config.js';

// Import routes
import scanRoutes from './routes/scan.js';
import folderRoutes from './routes/folders.js';
import userMappingRoutes from './routes/user-mapping.js';
import migrationRoutes from './routes/migration.js';
import reportRoutes from './routes/reports.js';
import testErrorRoutes from './routes/test-error.js';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const app = express();
const PORT = process.env.PORT || 3000;

// Log startup configuration
console.log('🚀 Starting Drive-to-Lark Migrator Server');
console.log('📋 Configuration:');
console.log(`   🌐 Environment: ${process.env.NODE_ENV || 'development'}`);
console.log(`   🔌 Port: ${PORT}`);
console.log(`   🌐 Google Domain: ${process.env.GOOGLE_DOMAIN || 'Not set'}`);
console.log(`   👤 Google Admin: ${process.env.GOOGLE_ADMIN_EMAIL || 'Not set'}`);
console.log(`   📄 Service Account: ${process.env.GOOGLE_SERVICE_ACCOUNT_FILE || 'Not set'}`);

// Validate Google configuration on startup
try {
    const config = googleConfig.getConfig();
    console.log('✅ Google configuration validated successfully');
} catch (error) {
    console.error('❌ Google configuration validation failed:', error.message);
    console.error('   Please check your .env file and service account file');
    process.exit(1);
}

// Middleware
app.use(cors());
app.use(express.json({ limit: '10mb' }));
app.use(express.static(path.join(__dirname, '../frontend/public')));

// API Routes
app.use('/api/scan', scanRoutes);
app.use('/api/folders', folderRoutes);
app.use('/api/user-mapping', userMappingRoutes);
app.use('/api/migration', migrationRoutes);
app.use('/api/reports', reportRoutes);

// Test routes (only in development)
if (process.env.NODE_ENV !== 'production') {
    app.use('/api/test-error', testErrorRoutes);
}

// Health check endpoint
app.get('/health', (req, res) => {
    res.json({
        status: 'healthy',
        timestamp: new Date().toISOString(),
        environment: process.env.NODE_ENV,
        googleDomain: process.env.GOOGLE_DOMAIN,
        version: '1.0.0'
    });
});

// Serve frontend
app.get('*', (req, res) => {
    res.sendFile(path.join(__dirname, '../frontend/public/index.html'));
});

app.listen(PORT, () => {
    console.log(`\n🌟 Server running on http://localhost:${PORT}`);
    console.log(`📊 Health check: http://localhost:${PORT}/health`);
    console.log('✅ Ready to accept requests\n');
});
