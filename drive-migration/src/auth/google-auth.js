import { GoogleAuth } from 'google-auth-library';
import { googleConfig } from '../config/google-config.js';
import dotenv from 'dotenv';

// Ensure environment variables are loaded
dotenv.config();

class GoogleAuthService {
    constructor() {
        this.auth = null;
        this.config = null;
        this.scopes = [
            'https://www.googleapis.com/auth/admin.directory.user.readonly',
            'https://www.googleapis.com/auth/admin.directory.user',
            'https://www.googleapis.com/auth/admin.directory.group.readonly',
            'https://www.googleapis.com/auth/drive',
            'https://www.googleapis.com/auth/drive.file',
            'https://www.googleapis.com/auth/drive.metadata',
            'https://www.googleapis.com/auth/drive.readonly'
        ];
    }

    async initialize(userEmail = null) {
        try {
            // Get fresh config from environment
            this.config = googleConfig.getConfig();
            const targetEmail = userEmail || this.config.adminEmail;

            console.log('🔐 Initializing Google Auth with configuration:');
            console.log(`   🌐 Domain: ${this.config.domain}`);
            console.log(`   📧 Target user: ${targetEmail}`);
            console.log(`   📄 Service account file: ${this.config.serviceAccountFile}`);
            console.log(`   📍 Service account path: ${this.config.serviceAccountPath}`);

            this.auth = new GoogleAuth({
                keyFile: this.config.serviceAccountPath,
                scopes: this.scopes,
                clientOptions: {
                    subject: targetEmail
                }
            });

            // Test authentication
            const authClient = await this.auth.getClient();
            const tokenInfo = await authClient.getAccessToken();

            console.log('✅ Google authentication successful');
            console.log(`   🎫 Token acquired: ${tokenInfo.token ? 'Yes' : 'No'}`);

            return {
                success: true,
                userEmail: targetEmail,
                domain: this.config.domain,
                serviceAccountFile: this.config.serviceAccountFile
            };

        } catch (error) {
            console.error('❌ Google authentication failed:', error.message);
            console.error('   Configuration used:');
            console.error(`   - Domain: ${this.config?.domain || 'Not loaded'}`);
            console.error(`   - Admin Email: ${this.config?.adminEmail || 'Not loaded'}`);
            console.error(`   - Service Account File: ${this.config?.serviceAccountFile || 'Not loaded'}`);

            return { success: false, error: error.message };
        }
    }

    async getAuthClient(userEmail = null) {
        if (!this.auth) {
            await this.initialize(userEmail);
        }
        return this.auth.getClient();
    }

    getConfig() {
        return this.config || googleConfig.getConfig();
    }

    // Method to reinitialize with new configuration
    async reinitialize(userEmail = null) {
        this.auth = null;
        this.config = null;
        googleConfig.reload(); // Reload environment variables
        return await this.initialize(userEmail);
    }
}

export const googleAuth = new GoogleAuthService();
export default GoogleAuthService;
