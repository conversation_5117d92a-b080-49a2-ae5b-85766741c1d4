import dotenv from 'dotenv';
import { googleConfig } from './config/google-config.js';
import { googleAuth } from './auth/google-auth.js';

// Load environment variables
dotenv.config();

console.log('🏗️ Drive-to-Lark Migrator - Infrastructure Check\n');
console.log('='.repeat(70));

async function checkInfrastructure() {
    const userEmail = process.argv[2] || googleConfig.getAdminEmail();

    console.log('📋 Configuration Summary:');
    console.log(`   🌐 Google Domain: ${googleConfig.getDomain()}`);
    console.log(`   👤 Admin Email: ${userEmail}`);
    console.log(`   📄 Service Account File: ${googleConfig.getServiceAccountFile()}`);
    console.log('');

    const results = {
        environment: { success: false, details: {} },
        googleConfig: { success: false, details: {} },
        database: { success: false, details: {} },
        googleAuth: { success: false, details: {} },
        larkAuth: { success: false, details: {} }
    };

    // 1. Environment Variables Check
    console.log('1. 🔧 Environment Variables Check');
    console.log('-'.repeat(50));

    const requiredEnvVars = [
        'GOOGLE_SERVICE_ACCOUNT_FILE',
        'GOOGLE_DOMAIN',
        'GOOGLE_ADMIN_EMAIL',
        'LARK_APP_ID',
        'LARK_APP_SECRET',
        'SUPABASE_URL',
        'SUPABASE_ANON_KEY',
        'SUPABASE_SERVICE_ROLE_KEY'
    ];

    const missingVars = [];
    const presentVars = [];

    for (const envVar of requiredEnvVars) {
        if (!process.env[envVar]) {
            missingVars.push(envVar);
            console.log(`❌ ${envVar}: Not set`);
        } else {
            presentVars.push(envVar);
            // Show partial value for security
            const value = process.env[envVar];
            const displayValue = value.length > 20 ?
                `${value.substring(0, 10)}...${value.substring(value.length - 5)}` :
                value;
            console.log(`✅ ${envVar}: ${displayValue}`);
        }
    }

    if (missingVars.length === 0) {
        results.environment.success = true;
        results.environment.details = { presentVars, missingVars: [] };
        console.log('✅ All required environment variables are set');
    } else {
        results.environment.details = { presentVars, missingVars };
        console.log(`❌ Missing ${missingVars.length} required environment variables`);
    }

    // 2. Google Configuration Check
    console.log('\n2. 📄 Google Configuration Check');
    console.log('-'.repeat(50));

    try {
        const config = googleConfig.getConfig();
        console.log(`✅ Service Account File: ${config.serviceAccountFile}`);
        console.log(`✅ Service Account Path: ${config.serviceAccountPath}`);
        console.log(`✅ Domain: ${config.domain}`);
        console.log(`✅ Admin Email: ${config.adminEmail}`);
        console.log(`✅ Credentials loaded successfully`);
        console.log(`✅ Project ID: ${config.credentials.project_id}`);
        console.log(`✅ Client Email: ${config.credentials.client_email}`);

        results.googleConfig.success = true;
        results.googleConfig.details = {
            serviceAccountFile: config.serviceAccountFile,
            domain: config.domain,
            adminEmail: config.adminEmail,
            projectId: config.credentials.project_id,
            clientEmail: config.credentials.client_email
        };
    } catch (error) {
        console.log(`❌ Google configuration error: ${error.message}`);
        results.googleConfig.details.error = error.message;
    }

    // 3. Google Authentication Check
    console.log('\n3. 🔐 Google Authentication Check');
    console.log('-'.repeat(50));

    try {
        const authResult = await googleAuth.initialize(userEmail);
        if (authResult.success) {
            console.log(`✅ Google authentication successful`);
            console.log(`   📧 User: ${authResult.userEmail}`);
            console.log(`   🌐 Domain: ${authResult.domain}`);
            console.log(`   📄 Service Account: ${authResult.serviceAccountFile}`);

            results.googleAuth.success = true;
            results.googleAuth.details = {
                userEmail: authResult.userEmail,
                domain: authResult.domain,
                serviceAccountFile: authResult.serviceAccountFile
            };
        } else {
            console.log(`❌ Google authentication failed: ${authResult.error}`);
            results.googleAuth.details.error = authResult.error;
        }
    } catch (error) {
        console.log(`❌ Google authentication error: ${error.message}`);
        results.googleAuth.details.error = error.message;
    }

    return results;
}

// Run the check
checkInfrastructure()
    .then(results => {
        console.log('\n' + '='.repeat(70));
        console.log('📊 Infrastructure Check Summary:');
        console.log(`   Environment Variables: ${results.environment.success ? '✅' : '❌'}`);
        console.log(`   Google Configuration: ${results.googleConfig.success ? '✅' : '❌'}`);
        console.log(`   Google Authentication: ${results.googleAuth.success ? '✅' : '❌'}`);

        if (results.environment.success && results.googleConfig.success && results.googleAuth.success) {
            console.log('\n🎉 All infrastructure checks passed!');
            process.exit(0);
        } else {
            console.log('\n⚠️  Some infrastructure checks failed. Please review the errors above.');
            process.exit(1);
        }
    })
    .catch(error => {
        console.error('💥 Infrastructure check failed:', error.message);
        process.exit(1);
    });
