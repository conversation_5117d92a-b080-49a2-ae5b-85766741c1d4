import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

class GoogleConfig {
    constructor() {
        // Load from environment variables with fallbacks
        this.serviceAccountFile = process.env.GOOGLE_SERVICE_ACCOUNT_FILE || 'google-service-account.json';
        this.domain = process.env.GOOGLE_DOMAIN || 'osp.com.vn';
        this.adminEmail = process.env.GOOGLE_ADMIN_EMAIL || `admin@${this.domain}`;
        
        console.log('🔧 Loading Google Configuration:');
        console.log(`   📄 Service Account File: ${this.serviceAccountFile}`);
        console.log(`   🌐 Domain: ${this.domain}`);
        console.log(`   👤 Admin Email: ${this.adminEmail}`);
        
        this.validateConfig();
    }

    validateConfig() {
        // Validate service account file exists
        const serviceAccountPath = this.getServiceAccountPath();
        if (!fs.existsSync(serviceAccountPath)) {
            throw new Error(`Google Service Account file not found: ${serviceAccountPath}`);
        }

        // Validate domain format
        if (!this.domain || !this.domain.includes('.')) {
            throw new Error(`Invalid Google domain: ${this.domain}`);
        }

        // Validate admin email matches domain
        if (!this.adminEmail.endsWith(`@${this.domain}`)) {
            console.warn(`⚠️  Warning: Admin email domain (${this.adminEmail}) doesn't match configured domain (${this.domain})`);
        }

        console.log('✅ Google configuration validated');
    }

    getServiceAccountPath() {
        // Support both absolute and relative paths
        if (path.isAbsolute(this.serviceAccountFile)) {
            return this.serviceAccountFile;
        }
        
        // Relative to project root
        return path.resolve(path.join(__dirname, '../../', this.serviceAccountFile));
    }

    getServiceAccountCredentials() {
        const serviceAccountPath = this.getServiceAccountPath();
        try {
            const credentials = JSON.parse(fs.readFileSync(serviceAccountPath, 'utf8'));
            console.log(`✅ Service account credentials loaded from: ${serviceAccountPath}`);
            return credentials;
        } catch (error) {
            throw new Error(`Failed to load Google Service Account credentials: ${error.message}`);
        }
    }

    getDomain() {
        return this.domain;
    }

    getAdminEmail() {
        return this.adminEmail;
    }

    getServiceAccountFile() {
        return this.serviceAccountFile;
    }

    getConfig() {
        return {
            serviceAccountFile: this.serviceAccountFile,
            serviceAccountPath: this.getServiceAccountPath(),
            domain: this.domain,
            adminEmail: this.adminEmail,
            credentials: this.getServiceAccountCredentials()
        };
    }

    // Method to reload configuration (useful for testing different configs)
    reload() {
        dotenv.config({ override: true });
        this.serviceAccountFile = process.env.GOOGLE_SERVICE_ACCOUNT_FILE || 'google-service-account.json';
        this.domain = process.env.GOOGLE_DOMAIN || 'osp.com.vn';
        this.adminEmail = process.env.GOOGLE_ADMIN_EMAIL || `admin@${this.domain}`;
        this.validateConfig();
    }
}

export const googleConfig = new GoogleConfig();
export default GoogleConfig;